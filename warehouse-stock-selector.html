<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>锁货条件</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        
        .delivery-method {
            margin-bottom: 20px;
        }
        
        .delivery-method label {
            margin-right: 15px;
            font-size: 14px;
        }
        
        .delivery-method input[type="checkbox"] {
            margin-right: 5px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        
        .section-title {
            font-size: 14px;
            margin-bottom: 15px;
            color: #666;
        }
        
        .form-row {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 10px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .form-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            min-width: 150px;
            background-color: white;
        }
        
        .form-group input[type="number"] {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            width: 100px;
        }
        
        .add-warehouse-btn {
            padding: 8px 15px;
            background-color: #fff;
            border: 1px solid #ff9500;
            color: #ff9500;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .add-warehouse-btn:hover {
            background-color: #fff5e6;
        }
        
        .warehouse-item {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 10px;
            padding: 10px;
            background-color: #fafafa;
            border-radius: 4px;
        }
        
        .remove-btn {
            background-color: #ff4d4f;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .remove-btn:hover {
            background-color: #ff7875;
        }
        
        .error-message {
            color: #ff4d4f;
            font-size: 12px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">锁货条件</div>
        
        <div class="delivery-method">
            <label><input type="checkbox" checked> 自提</label>
            <label><input type="checkbox" checked> 一件代发</label>
        </div>
        
        <!-- 自提部分 -->
        <div class="section">
            <div class="section-title">* 请选择仓库和可锁定的自提库存</div>
            <div id="pickup-warehouses">
                <div class="warehouse-item">
                    <div class="form-group">
                        <label>仓库</label>
                        <select class="warehouse-select" data-type="pickup">
                            <option value="">请选择仓库</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>可锁定库存</label>
                        <input type="number" class="stock-input" data-type="pickup" min="0" placeholder="0">
                    </div>
                    <button class="remove-btn" onclick="removeWarehouse(this)" style="display: none;">删除</button>
                </div>
            </div>
            <button class="add-warehouse-btn" onclick="addWarehouse('pickup')">+ 新增仓库</button>
        </div>
        
        <!-- 一件代发部分 -->
        <div class="section">
            <div class="section-title">* 请选择仓库和可锁定的一件代发库存</div>
            <div id="dropshipping-warehouses">
                <div class="warehouse-item">
                    <div class="form-group">
                        <label>仓库</label>
                        <select class="warehouse-select" data-type="dropshipping">
                            <option value="">请选择仓库</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>可锁定库存</label>
                        <input type="number" class="stock-input" data-type="dropshipping" min="0" placeholder="0">
                    </div>
                    <button class="remove-btn" onclick="removeWarehouse(this)" style="display: none;">删除</button>
                </div>
            </div>
            <button class="add-warehouse-btn" onclick="addWarehouse('dropshipping')">+ 新增仓库</button>
        </div>
    </div>

    <script>
        // 模拟后端数据
        const warehouseData = [
            {
                "id": "1914939867699949570",
                "productSkuCode": "HJ169935",
                "pickUpStockTotal": 100,
                "dropShippingStockTotal": 100,
                "warehouseCode": "EFCA",
                "warehouseSystemCode": "CA283055"
            },
            {
                "id": "1914939867704143878",
                "productSkuCode": "HJ169935",
                "pickUpStockTotal": 456,
                "dropShippingStockTotal": 456,
                "warehouseCode": "EFTX01",
                "warehouseSystemCode": "supplier520378"
            },
            {
                "id": "1914939867704143887",
                "productSkuCode": "HJ169935",
                "pickUpStockTotal": 422,
                "dropShippingStockTotal": 422,
                "warehouseCode": "EFNJ04",
                "warehouseSystemCode": "supplier190668"
            },
            {
                "id": "1914939867704143893",
                "productSkuCode": "HJ169935",
                "pickUpStockTotal": 100,
                "dropShippingStockTotal": 100,
                "warehouseCode": "EFWA",
                "warehouseSystemCode": "supplier313422"
            },
            {
                "id": "1914939867704143888",
                "productSkuCode": "HJ169935",
                "pickUpStockTotal": 372,
                "dropShippingStockTotal": 372,
                "warehouseCode": "EFGA01",
                "warehouseSystemCode": "supplier335295"
            }
        ];

        // 存储当前已分配的库存
        let allocatedStock = {};

        // 初始化页面
        function initPage() {
            // 初始化已分配库存记录
            warehouseData.forEach(warehouse => {
                allocatedStock[warehouse.warehouseCode] = {
                    pickup: 0,
                    dropshipping: 0
                };
            });
            
            // 初始化下拉框选项
            updateAllSelects();
        }

        // 更新所有下拉框选项
        function updateAllSelects() {
            const selects = document.querySelectorAll('.warehouse-select');
            selects.forEach(select => {
                updateSelectOptions(select);
            });
        }

        // 更新单个下拉框选项
        function updateSelectOptions(select) {
            const currentValue = select.value;
            const type = select.dataset.type;
            
            // 清空选项
            select.innerHTML = '<option value="">请选择仓库</option>';
            
            // 添加可用仓库选项
            warehouseData.forEach(warehouse => {
                const stockType = type === 'pickup' ? 'pickUpStockTotal' : 'dropShippingStockTotal';
                const originalStock = warehouse[stockType];
                
                if (originalStock > 0) {
                    const allocated = allocatedStock[warehouse.warehouseCode];
                    const otherTypeAllocated = type === 'pickup' ? allocated.dropshipping : allocated.pickup;
                    const availableStock = originalStock - otherTypeAllocated;
                    
                    if (availableStock > 0 || currentValue === warehouse.warehouseCode) {
                        const option = document.createElement('option');
                        option.value = warehouse.warehouseCode;
                        option.textContent = `${warehouse.warehouseCode}(${availableStock})`;
                        option.dataset.maxStock = availableStock;
                        select.appendChild(option);
                    }
                }
            });
            
            // 恢复之前选中的值
            if (currentValue) {
                select.value = currentValue;
            }
        }

        // 添加仓库行
        function addWarehouse(type) {
            const container = document.getElementById(type + '-warehouses');
            const newItem = document.createElement('div');
            newItem.className = 'warehouse-item';
            newItem.innerHTML = `
                <div class="form-group">
                    <label>仓库</label>
                    <select class="warehouse-select" data-type="${type}">
                        <option value="">请选择仓库</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>可锁定库存</label>
                    <input type="number" class="stock-input" data-type="${type}" min="0" placeholder="0">
                </div>
                <button class="remove-btn" onclick="removeWarehouse(this)">删除</button>
            `;
            
            container.appendChild(newItem);
            
            // 为新添加的元素绑定事件
            const select = newItem.querySelector('.warehouse-select');
            const input = newItem.querySelector('.stock-input');
            
            updateSelectOptions(select);
            bindEvents(select, input);
        }

        // 删除仓库行
        function removeWarehouse(button) {
            const item = button.closest('.warehouse-item');
            const select = item.querySelector('.warehouse-select');
            const input = item.querySelector('.stock-input');
            
            // 释放已分配的库存
            if (select.value && input.value) {
                const type = select.dataset.type;
                allocatedStock[select.value][type] -= parseInt(input.value) || 0;
            }
            
            item.remove();
            updateAllSelects();
        }

        // 绑定事件
        function bindEvents(select, input) {
            select.addEventListener('change', function() {
                const type = this.dataset.type;
                const warehouseCode = this.value;
                
                if (warehouseCode) {
                    // 更新输入框的最大值
                    const option = this.querySelector(`option[value="${warehouseCode}"]`);
                    const maxStock = parseInt(option.dataset.maxStock);
                    input.max = maxStock;
                    input.value = Math.min(parseInt(input.value) || 0, maxStock);
                } else {
                    input.max = '';
                    input.value = '';
                }
                
                updateStockAllocation();
            });
            
            input.addEventListener('input', function() {
                const max = parseInt(this.max);
                if (max && parseInt(this.value) > max) {
                    this.value = max;
                }
                updateStockAllocation();
            });
        }

        // 更新库存分配
        function updateStockAllocation() {
            // 重置分配记录
            Object.keys(allocatedStock).forEach(warehouseCode => {
                allocatedStock[warehouseCode] = {
                    pickup: 0,
                    dropshipping: 0
                };
            });
            
            // 重新计算分配
            const items = document.querySelectorAll('.warehouse-item');
            items.forEach(item => {
                const select = item.querySelector('.warehouse-select');
                const input = item.querySelector('.stock-input');
                const type = select.dataset.type;
                
                if (select.value && input.value) {
                    const warehouseCode = select.value;
                    const quantity = parseInt(input.value) || 0;
                    allocatedStock[warehouseCode][type] += quantity;
                }
            });
            
            // 更新所有下拉框
            updateAllSelects();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initPage();
            
            // 为初始元素绑定事件
            const selects = document.querySelectorAll('.warehouse-select');
            const inputs = document.querySelectorAll('.stock-input');
            
            selects.forEach((select, index) => {
                bindEvents(select, inputs[index]);
            });
        });
    </script>
</body>
</html>
