package com.zsmall.order.biz.support;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.activity.biz.support.ProductActiveSupper;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivity;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityStock;
import com.zsmall.activity.entity.iservice.IDistributorProductActivityPriceService;
import com.zsmall.activity.entity.iservice.IDistributorProductActivityService;
import com.zsmall.activity.entity.iservice.IDistributorProductActivityStockService;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.OrderExceptionEnum;
import com.zsmall.common.enums.order.OrderIsReleaseEnum;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.productActivity.ProductActivityExceptionEnum;
import com.zsmall.common.enums.productActivity.ProductActivityStateEnum;
import com.zsmall.common.enums.statuscode.OrderStatusCodeEnum;
import com.zsmall.common.util.RetryUtil;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.iservice.IOrdersService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2025年7月10日  16:45
 * @description: 订单活动支持类
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderActivitySupport {

    private final IDistributorProductActivityService distributorProductActivityService;
    private final IDistributorProductActivityStockService distributorProductActivityStockService;
    private final IDistributorProductActivityPriceService distributorProductActivityPriceService;
    private final IOrdersService iOrdersService;
    private final ProductActiveSupper productActiveSupper;

    /**
     * 获取分销商活动
     * @param tenantId
     * @param productSkuCode
     * @param site
     * @param supportedLogistics
     * @return
     */
    @InMethodLog("获取分销商活动")
    public List<DistributorProductActivity> getDistributorActivity(String tenantId, String productSkuCode,String site,String supportedLogistics) {
        // todo 250711 发货方式需要修改，活动里有两者都支持的发货方式，字段不明
        // 根据分销商租户Id、productSkuCode、活动状态获取分销商活动
        LambdaQueryWrapper<DistributorProductActivity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DistributorProductActivity::getDistributorTenantId, tenantId);
        queryWrapper.eq(DistributorProductActivity::getProductSkuCode, productSkuCode);
        queryWrapper.eq(DistributorProductActivity::getSite, site);
        queryWrapper.eq(DistributorProductActivity::getSupportedLogistics, supportedLogistics);
        queryWrapper.eq(DistributorProductActivity::getExceptionCode, ProductActivityExceptionEnum.NOT_EXCEPTION.getCode());
        queryWrapper.in(DistributorProductActivity::getActivityState, Arrays.asList(ProductActivityStateEnum.InProgress.name()));
        List<DistributorProductActivity> distributorProductActivityList = distributorProductActivityService.list(queryWrapper);
        if(CollUtil.isEmpty(distributorProductActivityList)){
            log.info("没有获取到分销商活动,tenantId:{},productSkuCode:{}",tenantId,productSkuCode);
            return null;
        }
        return null;
    }


    /**
     * 根据SKU和站点以及发货方式获取有活动库存的活动
     *
     * @param tenantId
     * @param productSkuCode
     * @param site
     * @param supportedLogistics
     * @return
     */
    public DistributorProductActivity getDistributorActivityByStock(String tenantId, String productSkuCode,String site,String supportedLogistics) {
        // 根据分销商租户Id、productSkuCode、活动状态获取分销商活动
        LambdaQueryWrapper<DistributorProductActivity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DistributorProductActivity::getDistributorTenantId, tenantId);
        queryWrapper.eq(DistributorProductActivity::getProductSkuCode, productSkuCode);
        queryWrapper.eq(DistributorProductActivity::getSite, site);
        queryWrapper.eq(DistributorProductActivity::getExceptionCode, ProductActivityExceptionEnum.NOT_EXCEPTION.getCode());
        queryWrapper.in(DistributorProductActivity::getSupportedLogistics, supportedLogistics, SupportedLogisticsEnum.All.name());
        queryWrapper.in(DistributorProductActivity::getActivityState, Arrays.asList(ProductActivityStateEnum.InProgress.name()));
        List<DistributorProductActivity> distributorProductActivityList = distributorProductActivityService.list(queryWrapper);
        if(CollUtil.isEmpty(distributorProductActivityList)){
            log.info("没有获取到分销商活动,tenantId:{},productSkuCode:{}",tenantId,productSkuCode);
            return null;
        }
        for(DistributorProductActivity distributorProductActivity : distributorProductActivityList){
            List<DistributorProductActivityStock> distributorProductActivityStockList = distributorProductActivityStockService.list(
                new LambdaQueryWrapper<DistributorProductActivityStock>().eq(DistributorProductActivityStock::getDistributorActivityId, distributorProductActivity.getId()));
            if(CollUtil.isNotEmpty(distributorProductActivityStockList)){
               for(DistributorProductActivityStock distributorProductActivityStock : distributorProductActivityStockList){
                   if(distributorProductActivityStock.getQuantitySurplus() > 0){
                       return distributorProductActivity;
                   }
               }
            }
        }
        return null;
    }

    /**
     * 获取活动仓库信息,判断活动剩余库存数量，返回第一个锁货剩余数量大于0的仓库信息
     * @param distributorProductActivity
     * @return
     */
    @InMethodLog("获取活动仓库信息")
    public DistributorProductActivityStock getActivityWarehouseInfo(DistributorProductActivity distributorProductActivity) {
        if(null == distributorProductActivity || null == distributorProductActivity.getId()){
            log.info("参数为空，{}",distributorProductActivity);
            return null;
        }
        List<DistributorProductActivityStock> distributorProductActivityStockList = distributorProductActivityStockService.list(
            new LambdaQueryWrapper<DistributorProductActivityStock>().eq(DistributorProductActivityStock::getDistributorActivityId, distributorProductActivity.getId()));
        if(CollUtil.isEmpty(distributorProductActivityStockList)){
            log.info("没有获取到活动仓库信息,distributorProductActivityId:{}",distributorProductActivity.getId());
            return null;
        }
        for(DistributorProductActivityStock distributorProductActivityStock : distributorProductActivityStockList){
            if(distributorProductActivityStock.getQuantitySurplus() > 0){
                return distributorProductActivityStock;
            }
        }
        return null;
    }

    /**
     * 获取活动可用库存的仓库信息
     * @param activityCode
     * @param quantity
     * @return
     */
    @InMethodLog("获取活动可用库存的仓库信息")
    public List<DistributorProductActivityStock> getActivityAvailableWarehouseInfo(String activityCode,Integer quantity) {
        if(null == activityCode || null == quantity){
            log.info("活动code为空:{}，数量为空:{}",activityCode,quantity);
            return null;
        }
        DistributorProductActivity distributorProductActivity = TenantHelper.ignore(() ->
            distributorProductActivityService.getOne(new LambdaQueryWrapper<DistributorProductActivity>().eq(DistributorProductActivity::getDistributorActivityCode, activityCode)
                                                                                                         .eq(DistributorProductActivity::getExceptionCode, ProductActivityExceptionEnum.NOT_EXCEPTION.getCode()))
        );
        if(null == distributorProductActivity || null == distributorProductActivity.getActivityState()){
            log.info("没有获取到活动信息,activityCode:{},活动状态为null",activityCode);
            return null;
        }
        if(!ProductActivityStateEnum.InProgress.name().equals(distributorProductActivity.getActivityState())){
            log.info("活动{} 不在进行中",activityCode);
            return null;
        }
        List<DistributorProductActivityStock> distributorProductActivityStockList = TenantHelper.ignore(() ->distributorProductActivityStockService.list(
            new LambdaQueryWrapper<DistributorProductActivityStock>().eq(DistributorProductActivityStock::getDistributorActivityCode, activityCode)
                .lt(DistributorProductActivityStock::getQuantitySurplus, quantity)
        ));
        return distributorProductActivityStockList;
    }

    /**
     * 订单的物流类型和活动物流类型转换
     * @param logisticsType
     */
    @InMethodLog("订单的物流类型和活动物流类型转换")
    public String logisticsTypeConvert(LogisticsTypeEnum logisticsType){
        if(null == logisticsType){
            log.info("物流类型为空:{}",logisticsType);
            return null;
        }
        switch (logisticsType){
            case PickUp:
                return SupportedLogisticsEnum.PickUpOnly.name();
            case DropShipping:
            case ZSMallDropShipping:
                return SupportedLogisticsEnum.DropShippingOnly.name();
            default:
                return null;
        }
    }

    /**
     * 判断活动订单的活动是否有异常状态
     * @param ordersList
     * @return  Boolean true:有异常状态 false:没有异常状态
     */
    @InMethodLog("判断活动订单的活动是否有异常状态")
    public Boolean checkActivityException(List<Orders> ordersList) {
        if (CollUtil.isEmpty(ordersList)){
            return Boolean.FALSE;
        }
        Boolean isException = Boolean.FALSE;
        for(Orders orders : ordersList){
            String activityCode = orders.getActivityCode();
            if(StringUtils.isEmpty(activityCode)){
                continue;
            }
            DistributorProductActivity distributorProductActivity = TenantHelper.ignore(() ->
                distributorProductActivityService.getOne(new LambdaQueryWrapper<DistributorProductActivity>().eq(DistributorProductActivity::getDistributorActivityCode, activityCode)));
            if(null == distributorProductActivity){
                continue;
            }
            if(null != distributorProductActivity.getActivityState() && !ProductActivityStateEnum.InProgress.getValue().equals(distributorProductActivity.getActivityState())){
                log.info("分销商活动{} 状态不是进行中", activityCode);
                continue;
            }
            if (null != distributorProductActivity.getExceptionCode() && ProductActivityExceptionEnum.NOT_EXCEPTION.getCode() != distributorProductActivity.getExceptionCode()){
                isException = Boolean.TRUE;
                orders.setOrderState(OrderStateType.Failed);
                orders.setExceptionCode(OrderExceptionEnum.activity_exception.getValue());
                // 根据不同的异常，赋值错误信息
                int exceptionCode = distributorProductActivity.getExceptionCode();
                if (exceptionCode == ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode()) {
                    orders.setPayErrorMessage(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_ACTIVITY_EXCEPTION_STOCK_PULL_LOCK_EXCEPTION)
                                                           .toJSON());
                } else if (exceptionCode == ProductActivityExceptionEnum.ERP_LOCK_EXCEPTION.getCode()) {
                    orders.setPayErrorMessage(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_ACTIVITY_EXCEPTION_ERP_LOCK_EXCEPTION)
                                                           .toJSON());
                } else if (exceptionCode == ProductActivityExceptionEnum.ERP_RELEASE_EXCEPTION.getCode()) {
                    orders.setPayErrorMessage(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_ACTIVITY_EXCEPTION_ERP_RELEASE_EXCEPTION)
                                                           .toJSON());
                }
                iOrdersService.updateById(orders);
            }
        }
        return isException;
    }

    /**
     * 调用erp释放库存接口
     * @param orderList
     */
    @InMethodLog("erp释放库存接口-批量处理")
    public void releaseInventoryBatch(List<Orders> orderList){
        for (Orders orders : orderList){
            String activityCode = orders.getActivityCode();
            if(StringUtils.isEmpty(activityCode)){
                continue;
            }
            DistributorProductActivity distributorProductActivity = TenantHelper.ignore(() ->
                distributorProductActivityService.getOne(new LambdaQueryWrapper<DistributorProductActivity>().eq(DistributorProductActivity::getDistributorActivityCode, activityCode)
                                                                                                             .eq(DistributorProductActivity::getExceptionCode, ProductActivityExceptionEnum.NOT_EXCEPTION.getCode())
                                                                                                             .in(DistributorProductActivity::getActivityState, List.of(ProductActivityStateEnum.InProgress.name()))));
            if(null == distributorProductActivity){
                continue;
            }
            // 调用erp释放库存接口，失败的情况下重试三次，每次间隔3秒
            boolean success = RetryUtil.executeWithRetry(
                () -> productActiveSupper.productLockInventoryReleaseOpenApi(
                    distributorProductActivity.getDistributorActivityCode(),
                    distributorProductActivity.getId(),
                    orders.getTotalQuantity()
                ),
                3,
                3000
            );
            if (!success) {
                log.info("订单{}活动{}释放库存失败", orders.getOrderNo(), distributorProductActivity.getDistributorActivityCode());
                orders.setPayErrorMessage(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_EXCEPTION_RELEASE_EXCEPTION)
                                                       .toJSON());
                orders.setExceptionCode(OrderExceptionEnum.abnormal_exception.getValue());
                iOrdersService.updateById(orders);
            }else {
                log.info("订单{}活动{}释放库存成功", orders.getOrderNo(), distributorProductActivity.getDistributorActivityCode());
                orders.setIsRelease(OrderIsReleaseEnum.Success.getValue());
                iOrdersService.updateById(orders);
            }

        }

    }

    /**
     * erp释放库存接口
     * @param orders
     * @return
     */
    @InMethodLog("erp释放库存接口")
    public Boolean releaseInventory(Orders orders){
        String activityCode = orders.getActivityCode();
        if(StringUtils.isEmpty(activityCode)){
            return true;
        }
        Integer isRelease = orders.getIsRelease();
        if(null != isRelease && isRelease.equals(OrderIsReleaseEnum.Success.getValue())){
            return true;
        }
        DistributorProductActivity distributorProductActivity = TenantHelper.ignore(() ->
            distributorProductActivityService.getOne(new LambdaQueryWrapper<DistributorProductActivity>().eq(DistributorProductActivity::getDistributorActivityCode, activityCode)
                                                                                                         .eq(DistributorProductActivity::getExceptionCode, ProductActivityExceptionEnum.NOT_EXCEPTION.getCode())
                                                                                                         .in(DistributorProductActivity::getActivityState, List.of(ProductActivityStateEnum.InProgress.name()))));
        if(null == distributorProductActivity){
            return true;
        }
        // 调用erp释放库存接口，失败的情况下重试三次，每次间隔3秒
        boolean success = RetryUtil.executeWithRetry(
            () -> productActiveSupper.productLockInventoryReleaseOpenApi(
                distributorProductActivity.getDistributorActivityCode(),
                distributorProductActivity.getId(),
                orders.getTotalQuantity()
            ),
            3,
            3000
        );
        if (!success) {
            log.info("订单{}活动{}释放库存失败", orders.getOrderNo(), distributorProductActivity.getDistributorActivityCode());
            orders.setPayErrorMessage(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_EXCEPTION_RELEASE_EXCEPTION)
                                                   .toJSON());
            orders.setExceptionCode(OrderExceptionEnum.abnormal_exception.getValue());
            iOrdersService.updateById(orders);
            return false;
        }else {
            log.info("订单{}活动{}释放库存成功", orders.getOrderNo(), distributorProductActivity.getDistributorActivityCode());
            orders.setIsRelease(OrderIsReleaseEnum.Success.getValue());
            iOrdersService.updateById(orders);
            return true;
        }
    }


}
