1. 后端接口数据

```sql
{
    "code": 200,
    "subCode": null,
    "msg": "操作成功",
    "msgHtml": null,
    "data": [
        {
            "id": "1914939867699949570",
            "productSkuCode": "HJ169935",
            "pickUpStockTotal": 100,
            "dropShippingStockTotal": 100,
            "warehouseCode": "EFCA",
            "warehouseSystemCode": "CA283055"
        },
        {
            "id": "1914939867704143878",
            "productSkuCode": "HJ169935",
            "pickUpStockTotal": 456,
            "dropShippingStockTotal": 456,
            "warehouseCode": "EFTX01",
            "warehouseSystemCode": "supplier520378"
        },
        {
            "id": "1914939867704143887",
            "productSkuCode": "HJ169935",
            "pickUpStockTotal": 422,
            "dropShippingStockTotal": 422,
            "warehouseCode": "EFNJ04",
            "warehouseSystemCode": "supplier190668"
        },
        {
            "id": "1914939867704143893",
            "productSkuCode": "HJ169935",
            "pickUpStockTotal": 100,
            "dropShippingStockTotal": 100,
            "warehouseCode": "EFWA",
            "warehouseSystemCode": "supplier313422"
        },
        {
            "id": "1914939867704143888",
            "productSkuCode": "HJ169935",
            "pickUpStockTotal": 372,
            "dropShippingStockTotal": 372,
            "warehouseCode": "EFGA01",
            "warehouseSystemCode": "supplier335295"
        }
    ]
}
```

2. 现在的页面![](https://cdn.nlark.com/yuque/0/2025/png/22358749/1756107904886-1774c2bc-5d37-4606-95fb-559132f991f7.png)

如果上面返回的仓库库存dropShippingStockTotal>0 表示支持一件代发，pickUpStockTotal>0 表示支持自提   
下面是需求内容

1. 根据图片上的样式写一个简单的 html 页面
2. 如果当前仓库既支持一件代发也支持自提的话，则数据需要联动显示：例如  
上面输入 EFCA(100)  10,下面则变成 EFCA(90), 下面如果是 EFCA(100) 20，上面则是 EFCA(80)

